import React from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormText } from "@ant-design/pro-form";
import { addOfferTemplateLang } from "@/services/app/Offer/offer-template-lang";
import { Col, message, Modal, Row } from "antd";
import Util, { sn } from "@/util";
import { ProForm, ProFormTextArea, ProFormUploadDragger } from "@ant-design/pro-components";
import HtmlEditor from "@/components/HtmlEditor";
import { RcFile } from "antd/es/upload";
import { LS_TOKEN_NAME } from "@/constants";
import { deleteFile } from "@/services/app/File/file";
import styles from "./OfferTemplateLangFormPartial.less";

export type OfferTemplateLangFormPartialProps = {
  // formRef?: React.MutableRefObject<ProFormInstance>;
  // initialValues?: Partial<API.OfferTemplateLang>;
  renderSaveButton?: () => React.ReactNode;
};

const OfferTemplateLangFormPartial: React.FC<OfferTemplateLangFormPartialProps> = (props) => {
  const { renderSaveButton } = props;

  return (
    <div className={styles.langTemplateForm}>
      <Row gutter={24}>
        <Col span={12}>
          <ProFormText name={["DE", "subject"]} label={"Subject DE"} required rules={[{ required: true, message: "" }]} />
        </Col>
        <Col span={12}>
          <ProFormText name={["EN", "subject"]} label={"Subject EN"} required rules={[{ required: true, message: "" }]} />
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <ProFormTextArea name={["DE", "header"]} label={"Salutation DE"} tooltip="Keywords available: @Anrede" />
        </Col>
        <Col span={12}>
          <ProFormTextArea name={["EN", "header"]} label={"Salutation EN"} tooltip="Keywords available: @Anrede" />
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <ProForm.Item
            name={["DE", "body"]}
            label={<>Body DE</>}
            style={{ width: "100%" }}
            labelCol={undefined}
            wrapperCol={{ span: 24 }}
            rules={[
              {
                required: true,
                message: "Body content is required",
              },
            ]}
          >
            <HtmlEditor id={`offer_template_body_DE`} enableTextModule hideMenuBar toolbarMode={2} height={300} />
          </ProForm.Item>
        </Col>
        <Col span={12}>
          <ProForm.Item
            name={["EN", "body"]}
            label={<>Body EN</>}
            style={{ width: "100%" }}
            labelCol={undefined}
            wrapperCol={{ span: 24 }}
            rules={[
              {
                required: true,
                message: "Body content is required",
              },
            ]}
          >
            <HtmlEditor id={`offer_template_body_EN`} enableTextModule hideMenuBar toolbarMode={2} height={300} />
          </ProForm.Item>
        </Col>
      </Row>

      <Row gutter={24} justify="space-between" wrap={false}>
        <Col flex="auto">
          <ProFormUploadDragger
            name={["files"]}
            label={"Files"}
            title={false}
            description="Please select files or drag & drop"
            // accept="image/*"
            fieldProps={{
              multiple: true,
              listType: "picture",
              name: "file",
              style: { marginBottom: 24 },
              headers: {
                Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
              },
              beforeUpload: (file: RcFile, fileList: RcFile[]) => {
                return false;
              },
              onRemove: async (file: API.File) => {
                if (file.id) {
                  const { confirm } = Modal;
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: "Are you sure you want to delete?",
                      onOk: async () => {
                        resolve(true);
                        const hide = message.loading(`Deleting a file '${file.file_name}'.`, 0);
                        const res = await deleteFile(sn(file.id));
                        hide();
                        if (res) {
                          message.success(`Deleted successfully!`);
                        } else {
                          Util.error(`Delete failed, please try again!`);
                        }

                        return res;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                } else {
                  return true;
                }
              },
            }}
          />
        </Col>
        <Col style={{ paddingTop: 24 }}>{renderSaveButton?.()}</Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <ProFormTextArea
            name={["DE", "footer"]}
            label={<>Footer DE</>}
            style={{ width: "100%" }}
            labelCol={undefined}
            tooltip="Keywords available: @Username"
          />
        </Col>

        <Col span={12}>
          <ProFormTextArea
            name={["EN", "footer"]}
            label={<>Footer EN</>}
            style={{ width: "100%" }}
            labelCol={undefined}
            tooltip="Keywords available: @Username"
          />
        </Col>
      </Row>
    </div>
  );
};

export default OfferTemplateLangFormPartial;
