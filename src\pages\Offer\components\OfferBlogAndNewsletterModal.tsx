import { ProFormFieldSet } from "@ant-design/pro-components";
import { Modal } from "antd";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import OfferBlogUpdateForm from "./partials-blog-newsletter/OfferBlogUpdateForm";
import OfferNewsletterUpdateForm from "./partials-blog-newsletter/OfferNewsletterUpdateForm";

type RowType = APIOrg.OfferComment;

export type OfferBlogAndNewsletterModalProps = {
  offer_no: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const OfferBlogAndNewsletterModal: React.FC<OfferBlogAndNewsletterModalProps> = (props) => {
  const { offer_no, modalVisible, handleModalVisible } = props;

  const [reloadTickBlog, setReloadTickBlog] = useState(0);
  const [reloadTickNewsletter, setReloadTickNewsletter] = useState(0);

  useEffect(() => {
    setReloadTickBlog((prev) => prev + 1);
    setReloadTickNewsletter((prev) => prev + 1);
  }, [modalVisible]);

  return (
    <Modal title={`Offer Blog / Newsletter`} width="1400px" open={modalVisible} onCancel={() => handleModalVisible(false)} footer={false}>
      <h4 style={{ marginTop: 16 }}>Blog Details</h4>
      <OfferBlogUpdateForm offer_no={offer_no} reloadTick={reloadTickBlog} />

      <h4 style={{ marginTop: 16 }}>Newsletters</h4>
      <OfferNewsletterUpdateForm offer_no={offer_no} reloadTick={reloadTickNewsletter} />
    </Modal>
  );
};
export default OfferBlogAndNewsletterModal;
